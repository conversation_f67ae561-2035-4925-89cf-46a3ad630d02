import { Router, Request, Response } from 'express';
import { as<PERSON><PERSON><PERSON><PERSON> } from '../middleware/asyncHandler';
import { validateTenant } from '../middleware/validateTenant';
import { AppError } from '../utils/AppError';
import { logger } from '../utils/logger';
import { supabaseAdmin } from '../config/supabase';
import { zaloAuthService } from '../services/ZaloAuthService';
import { qrCleanupService } from '../services/QRCleanupService';

const router = Router();

// GET /api/qr-management/list - <PERSON><PERSON><PERSON> danh sách QR codes
router.get('/list', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;
  const { status, limit = 20, offset = 0 } = req.query;

  try {
    let query = supabaseAdmin
      .from('qr_codes')
      .select(`
        id,
        session_id,
        account_id,
        qr_image_url,
        status,
        expires_at,
        scanned_at,
        confirmed_at,
        chatwoot_inbox_id,
        metadata,
        created_at,
        updated_at
      `)
      .eq('tenant_id', tenantId)
      .order('created_at', { ascending: false })
      .range(Number(offset), Number(offset) + Number(limit) - 1);

    if (status) {
      query = query.eq('status', status);
    }

    const { data: qrCodes, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch QR codes: ${error.message}`);
    }

    res.json({
      success: true,
      message: 'QR codes retrieved successfully',
      data: {
        qr_codes: qrCodes,
        pagination: {
          limit: Number(limit),
          offset: Number(offset),
          total: qrCodes?.length || 0
        }
      }
    });

  } catch (error: any) {
    logger.error('Failed to fetch QR codes', {
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to fetch QR codes: ${error.message}`,
      500,
      'QR_FETCH_FAILED'
    );
  }
}));

// GET /api/qr-management/status/:sessionId - Kiểm tra trạng thái QR code
router.get('/status/:sessionId', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const { sessionId } = req.params;
  const tenantId = req.tenantId!;

  try {
    const { data: qrCode, error } = await supabaseAdmin
      .from('qr_codes')
      .select('*')
      .eq('session_id', sessionId)
      .eq('tenant_id', tenantId)
      .single();

    if (error || !qrCode) {
      throw new AppError('QR code not found', 404, 'QR_NOT_FOUND');
    }

    // Check if QR has expired
    const now = new Date();
    const expiresAt = new Date(qrCode.expires_at);
    
    if (now > expiresAt && qrCode.status === 'pending') {
      // Update status to expired
      await supabaseAdmin
        .from('qr_codes')
        .update({ 
          status: 'expired',
          updated_at: now.toISOString()
        })
        .eq('id', qrCode.id);
      
      qrCode.status = 'expired';
    }

    // Get account status if available
    let accountStatus = null;
    if (qrCode.account_id) {
      try {
        accountStatus = await zaloAuthService.getAccountStatus(qrCode.account_id, tenantId);
      } catch (error) {
        logger.warn('Failed to get account status', { 
          accountId: qrCode.account_id,
          error: (error as Error).message 
        });
      }
    }

    res.json({
      success: true,
      message: 'QR status retrieved successfully',
      data: {
        session_id: qrCode.session_id,
        status: qrCode.status,
        qr_image_url: qrCode.qr_image_url,
        expires_at: qrCode.expires_at,
        scanned_at: qrCode.scanned_at,
        confirmed_at: qrCode.confirmed_at,
        account_status: accountStatus,
        metadata: qrCode.metadata
      }
    });

  } catch (error: any) {
    if (error instanceof AppError) {
      throw error;
    }

    logger.error('Failed to check QR status', {
      sessionId,
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to check QR status: ${error.message}`,
      500,
      'QR_STATUS_CHECK_FAILED'
    );
  }
}));

// POST /api/qr-management/create - Tạo QR code mới
router.post('/create', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;
  const { 
    chatwoot_inbox_id, 
    expires_in_minutes = 10,
    user_agent 
  } = req.body;

  try {
    const result = await zaloAuthService.generateQRLogin({
      tenant_id: tenantId,
      user_agent: user_agent || req.get('User-Agent'),
      expires_in_minutes
    });

    // Update QR record with Chatwoot info if provided
    if (chatwoot_inbox_id) {
      await supabaseAdmin
        .from('qr_codes')
        .update({
          chatwoot_inbox_id: chatwoot_inbox_id,
          metadata: {
            ...result,
            chatwoot_inbox_id
          }
        })
        .eq('session_id', result.qr_session_id);
    }

    res.json({
      success: true,
      message: 'QR code created successfully',
      data: {
        account_id: result.account_id,
        session_id: result.qr_session_id,
        qr_code_url: result.qr_code_url,
        expires_at: result.expires_at,
        status: result.status,
        chatwoot_inbox_id: chatwoot_inbox_id || null,
        instructions: {
          step1: 'Display the QR code to user using qr_code_url',
          step2: 'User scans QR code with Zalo app',
          step3: 'Poll QR status using session_id',
          step4: 'Account will be automatically activated when login completes'
        }
      }
    });

  } catch (error: any) {
    logger.error('Failed to create QR code', {
      tenantId,
      chatwootInboxId: chatwoot_inbox_id,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to create QR code: ${error.message}`,
      500,
      'QR_CREATION_FAILED'
    );
  }
}));

// DELETE /api/qr-management/:sessionId - Xóa QR code
router.delete('/:sessionId', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const { sessionId } = req.params;
  const tenantId = req.tenantId!;

  try {
    const { data: qrCode, error: fetchError } = await supabaseAdmin
      .from('qr_codes')
      .select('*')
      .eq('session_id', sessionId)
      .eq('tenant_id', tenantId)
      .single();

    if (fetchError || !qrCode) {
      throw new AppError('QR code not found', 404, 'QR_NOT_FOUND');
    }

    // Delete from storage if exists
    if (qrCode.qr_image_url) {
      try {
        const storagePath = `qr-codes/${sessionId}.png`;
        await supabaseAdmin.storage
          .from('qr-codes')
          .remove([storagePath]);
      } catch (storageError) {
        logger.warn('Failed to delete QR from storage', { 
          sessionId, 
          error: (storageError as Error).message 
        });
      }
    }

    // Delete from database
    const { error: deleteError } = await supabaseAdmin
      .from('qr_codes')
      .delete()
      .eq('id', qrCode.id);

    if (deleteError) {
      throw new Error(`Failed to delete QR code: ${deleteError.message}`);
    }

    res.json({
      success: true,
      message: 'QR code deleted successfully',
      data: {
        session_id: sessionId,
        deleted_at: new Date().toISOString()
      }
    });

  } catch (error: any) {
    if (error instanceof AppError) {
      throw error;
    }

    logger.error('Failed to delete QR code', {
      sessionId,
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to delete QR code: ${error.message}`,
      500,
      'QR_DELETE_FAILED'
    );
  }
}));

// POST /api/qr-management/cleanup - Manual cleanup trigger
router.post('/cleanup', asyncHandler(async (req: Request, res: Response) => {
  try {
    const result = await qrCleanupService.manualCleanup();

    res.json({
      success: result.success,
      message: result.message,
      data: result.stats
    });

  } catch (error: any) {
    logger.error('Manual cleanup failed', {
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Manual cleanup failed: ${error.message}`,
      500,
      'CLEANUP_FAILED'
    );
  }
}));

// GET /api/qr-management/cleanup/status - Get cleanup service status
router.get('/cleanup/status', asyncHandler(async (req: Request, res: Response) => {
  try {
    const status = qrCleanupService.getStatus();

    res.json({
      success: true,
      message: 'Cleanup service status retrieved',
      data: status
    });

  } catch (error: any) {
    logger.error('Failed to get cleanup status', {
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to get cleanup status: ${error.message}`,
      500,
      'CLEANUP_STATUS_FAILED'
    );
  }
}));

export default router;
