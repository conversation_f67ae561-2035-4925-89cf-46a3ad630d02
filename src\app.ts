import express, { Application, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { logger } from './utils/logger';

// Import types
import './types/api';

// Declare Node.js globals
declare const process: any;

// Import routes
import testRoutes from './routes/test';
import zaloWebhookRoutes from './routes/zalo-webhook';
import zaloChatwootMappingRoutes from './routes/zalo-chatwoot-mapping';
import zaloAuthRoutes from './routes/zalo-auth';
import zaloMonitoringRoutes from './routes/zalo-monitoring';
import zaloIntegrationRoutes from './routes/zalo-integration';

// Import middleware
import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';

// Import services
import { zaloInitializationService } from './services/ZaloInitializationService';

class ExpressApp {
  public app: Application;
  private port: number;

  constructor(port: number = 3000) {
    this.app = express();
    this.port = port;
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet());
    
    // CORS configuration
    this.app.use(cors({
      origin: process.env.NODE_ENV === 'production' 
        ? ['https://yourdomain.com'] // Thay đổi domain của bạn
        : ['http://localhost:3000', 'http://localhost:3001'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Logging middleware
    if (process.env.NODE_ENV !== 'test') {
      this.app.use(morgan('combined', {
        stream: {
          write: (message: string) => {
            logger.info(message.trim());
          }
        }
      }));
    }

    // Request ID middleware
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      req.id = Math.random().toString(36).substring(2, 15);
      res.setHeader('X-Request-ID', req.id);
      next();
    });

    // Health check endpoint
    this.app.get('/health', (_req: Request, res: Response) => {
      res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development'
      });
    });
  }

  private initializeRoutes(): void {
    // API routes
    this.app.use('/api/test', testRoutes);
    this.app.use('/api/zalo-webhook', zaloWebhookRoutes);
    this.app.use('/api/zalo-chatwoot-mapping', zaloChatwootMappingRoutes);
    this.app.use('/api/zalo-auth', zaloAuthRoutes);
    this.app.use('/api/zalo-monitoring', zaloMonitoringRoutes);
    this.app.use('/api/zalo-integration', zaloIntegrationRoutes);

    // Root endpoint
    this.app.get('/', (req: Request, res: Response) => {
      res.json({
        message: 'Zalo Chatbot API',
        version: '1.0.0',
        endpoints: {
          health: '/health',
          test: '/api/test',
          zaloWebhook: '/api/zalo-webhook',
          zaloAuth: '/api/zalo-auth',
          zaloMapping: '/api/zalo-chatwoot-mapping',
          zaloMonitoring: '/api/zalo-monitoring',
          zaloIntegration: '/api/zalo-integration'
        }
      });
    });
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);
    
    // Global error handler
    this.app.use(errorHandler);
  }

  public async listen(): Promise<void> {
    // Initialize Zalo services before starting server
    try {
      logger.info('Initializing Zalo services...');
      await zaloInitializationService.initialize();
      logger.info('Zalo services initialized successfully');
    } catch (error: any) {
      logger.error('Failed to initialize Zalo services', { error: error.message });
      // Continue starting server even if Zalo services fail to initialize
    }

    this.app.listen(this.port, () => {
      logger.info(`🚀 Express server is running on port ${this.port}`);
      logger.info(`📍 Health check: http://localhost:${this.port}/health`);
      logger.info(`📍 API docs: http://localhost:${this.port}/api/test`);
      logger.info(`📍 Zalo Webhook: http://localhost:${this.port}/api/zalo-webhook`);
      logger.info(`📍 Zalo Auth: http://localhost:${this.port}/api/zalo-auth`);
      logger.info(`📍 Zalo Monitoring: http://localhost:${this.port}/api/zalo-monitoring`);
    });

    // Graceful shutdown
    process.on('SIGTERM', async () => {
      logger.info('SIGTERM received, shutting down gracefully');
      await zaloInitializationService.shutdown();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.info('SIGINT received, shutting down gracefully');
      await zaloInitializationService.shutdown();
      process.exit(0);
    });
  }

  public getApp(): Application {
    return this.app;
  }
}

export default ExpressApp;
