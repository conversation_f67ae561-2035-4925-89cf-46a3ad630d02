import { supabaseAdmin } from '../config/supabase';
import { logger } from '../utils/logger';
import { Zalo } from 'zca-js';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

// Interfaces
export interface ZaloAccount {
  id?: string;
  tenant_id: string;
  zalo_user_id: string;
  zalo_display_name?: string;
  zalo_phone?: string;
  zalo_avatar_url?: string;
  auth_data: {
    cookies: any;
    imei: string;
    user_agent: string;
    session_token?: string;
  };
  status: 'active' | 'inactive' | 'expired' | 'error' | 'pending' | 'qr_pending';
  qr_data?: {
    qr_code_path?: string;
    qr_status?: 'pending' | 'scanned' | 'confirmed' | 'expired' | 'failed';
    qr_expires_at?: string;
    qr_session_id?: string;
  };
  chatwoot_inbox_id?: number;
  chatwoot_source_id?: string;
  chatwoot_channel_status?: 'active' | 'inactive' | 'error';
  error_data?: {
    error_message?: string;
    error_code?: string;
    retry_count?: number;
    next_retry_at?: string;
  };
  login_method: 'qr' | 'cookie';
  settings?: any;
  metadata?: any;
  last_login_at?: string;
  last_activity_at?: string;
  expires_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface QRLoginRequest {
  tenant_id: string;
  user_agent?: string;
  expires_in_minutes?: number;
}

export interface QRLoginResponse {
  account_id: string;
  qr_code_path: string;
  qr_code_url?: string;
  qr_session_id: string;
  expires_at: string;
  status: string;
}

export interface CookieLoginRequest {
  tenant_id: string;
  cookies: any;
  imei: string;
  user_agent: string;
  zalo_user_id?: string;
  zalo_display_name?: string;
}

export interface AccountStatusResponse {
  account_id: string;
  zalo_user_id: string;
  status: string;
  is_connected: boolean;
  last_activity_at?: string;
  expires_at?: string;
  chatwoot_status?: string;
  error_message?: string;
}

export class ZaloAuthService {
  private zaloInstances: Map<string, any> = new Map(); // accountId -> Zalo instance
  private qrSessions: Map<string, any> = new Map(); // sessionId -> session data

  constructor() {
    this.initializeFromDatabase();
  }

  /**
   * Generate mock QR code for testing
   */
  private async generateMockQRCode(qrPath: string, sessionId: string, tenantId: string): Promise<void> {
    try {
      // Try to use qrcode library if available, otherwise create a simple image
      try {
        const QRCode = require('qrcode');
        const qrData = `zalo://login?session=${sessionId}&tenant=${tenantId}&timestamp=${Date.now()}`;

        await QRCode.toFile(qrPath, qrData, {
          type: 'png',
          width: 256,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });

        logger.info('Mock QR code generated with qrcode library', { qrPath, sessionId });

      } catch (qrError) {
        // Fallback: create a simple text file as placeholder
        const mockContent = `Mock QR Code\nSession: ${sessionId}\nTenant: ${tenantId}\nGenerated: ${new Date().toISOString()}`;
        fs.writeFileSync(qrPath, mockContent);

        logger.info('Mock QR code generated as text file', { qrPath, sessionId });
      }

    } catch (error: any) {
      logger.error('Failed to generate mock QR code', {
        sessionId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Upload QR code image to Supabase Storage
   */
  private async uploadQRToStorage(qrPath: string, sessionId: string): Promise<string> {
    try {
      // Read the QR code file
      const qrBuffer = fs.readFileSync(qrPath);

      // Generate storage path
      const storagePath = `qr-codes/${sessionId}.png`;

      // Upload to Supabase Storage
      const { data, error } = await supabaseAdmin.storage
        .from('qr-codes')
        .upload(storagePath, qrBuffer, {
          contentType: 'image/png',
          cacheControl: '3600',
          upsert: true
        });

      if (error) {
        throw new Error(`Failed to upload QR to storage: ${error.message}`);
      }

      // Get public URL
      const { data: publicUrlData } = supabaseAdmin.storage
        .from('qr-codes')
        .getPublicUrl(storagePath);

      logger.info('QR code uploaded to storage', {
        sessionId,
        storagePath,
        publicUrl: publicUrlData.publicUrl
      });

      return publicUrlData.publicUrl;

    } catch (error: any) {
      logger.error('Failed to upload QR to storage', {
        sessionId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Khởi tạo các Zalo instances từ database khi server start
   */
  private async initializeFromDatabase(): Promise<void> {
    try {
      logger.info('Initializing Zalo instances from database');

      const { data: accounts, error } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .eq('status', 'active');

      if (error) {
        logger.error('Failed to load active Zalo accounts', { error: error.message });
        return;
      }

      for (const account of accounts || []) {
        try {
          await this.initializeZaloInstance(account);
        } catch (error: any) {
          logger.error('Failed to initialize Zalo instance', {
            accountId: account.id,
            error: error.message
          });
        }
      }

      logger.info('Zalo instances initialized', { count: this.zaloInstances.size });
    } catch (error: any) {
      logger.error('Failed to initialize from database', { error: error.message });
    }
  }

  /**
   * Khởi tạo Zalo instance từ account data
   */
  private async initializeZaloInstance(account: ZaloAccount): Promise<any> {
    try {
      const zalo = new Zalo({
        selfListen: false,
        checkUpdate: false,
        logging: false
      });

      const api = await zalo.login({
        cookie: account.auth_data.cookies,
        imei: account.auth_data.imei,
        userAgent: account.auth_data.user_agent
      });

      // Setup listener for messages
      api.listener.start();
      
      // Store instance
      this.zaloInstances.set(account.id!, api);

      logger.info('Zalo instance initialized', {
        accountId: account.id,
        zaloUserId: account.zalo_user_id
      });

      return api;
    } catch (error: any) {
      logger.error('Failed to initialize Zalo instance', {
        accountId: account.id,
        error: error.message
      });
      
      // Update account status to error
      await this.updateAccountStatus(account.id!, account.tenant_id, 'error', {
        error_message: error.message,
        error_code: 'INIT_FAILED'
      });
      
      throw error;
    }
  }

  /**
   * Tạo QR code để đăng nhập
   */
  async generateQRLogin(request: QRLoginRequest): Promise<QRLoginResponse> {
    try {
      logger.info('Generating QR login', { tenantId: request.tenant_id });

      const qrSessionId = crypto.randomUUID();
      const expiresAt = new Date(Date.now() + (request.expires_in_minutes || 10) * 60 * 1000);
      const qrPath = path.join(process.cwd(), 'qr_codes', `${qrSessionId}.png`);

      // Ensure qr_codes directory exists
      const qrDir = path.dirname(qrPath);
      if (!fs.existsSync(qrDir)) {
        fs.mkdirSync(qrDir, { recursive: true });
      }

      // Create temporary account record first
      const { data: account, error } = await supabaseAdmin
        .from('zalo_accounts')
        .insert({
          tenant_id: request.tenant_id,
          zalo_user_id: 'pending_' + qrSessionId,
          status: 'qr_pending',
          auth_data: {
            user_agent: request.user_agent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            imei: '',
            cookies: {}
          },
          qr_data: {
            qr_session_id: qrSessionId,
            qr_status: 'pending',
            qr_expires_at: expiresAt.toISOString(),
            qr_code_path: qrPath
          },
          login_method: 'qr'
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create QR session: ${error.message}`);
      }

      // Create Zalo instance for QR login
      const zalo = new Zalo({
        selfListen: false,
        checkUpdate: false,
        logging: true // Enable logging for debugging
      });

      let api: any = null;
      let qrGenerated = false;

      try {
        // For now, create a mock QR code for testing
        // TODO: Fix zca-js QR generation issue
        const mockQRGeneration = process.env.NODE_ENV === 'development' || process.env.MOCK_QR === 'true';

        if (mockQRGeneration) {
          logger.info('Using mock QR generation for testing', { sessionId: qrSessionId });

          // Generate a real QR code using qrcode library
          await this.generateMockQRCode(qrPath, qrSessionId, request.tenant_id);

          qrGenerated = true;
          api = {
            mockApi: true,
            sessionId: qrSessionId,
            // Mock API methods
            listener: {
              start: () => logger.info('Mock listener started'),
              on: (event: string, callback: Function) => {
                logger.info('Mock listener event registered', { event });
              }
            }
          };

        } else {
          // Real QR generation with zca-js
          api = await zalo.loginQR({
            userAgent: request.user_agent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            qrPath: qrPath
          }, (event: any) => {
            logger.info('QR Event received', {
              type: event.type,
              sessionId: qrSessionId,
              event: event
            });

            if (event.type === 'qr-generated') {
              qrGenerated = true;
              logger.info('QR code generated successfully', { qrPath, sessionId: qrSessionId });
            } else if (event.type === 'qr-expired') {
              logger.warn('QR code expired', { sessionId: qrSessionId });
            } else if (event.type === 'error') {
              logger.error('QR generation error', {
                sessionId: qrSessionId,
                error: event.error || event.message
              });
            }
          });

          // Wait a bit for QR generation
          await new Promise(resolve => setTimeout(resolve, 2000));
        }

        // Check if QR file was actually created
        if (!fs.existsSync(qrPath)) {
          throw new Error('QR code file was not generated');
        }

        // Upload QR code to Supabase Storage
        const qrImageUrl = await this.uploadQRToStorage(qrPath, qrSessionId);

        // Create QR code record in database
        const { data: qrRecord, error: qrError } = await supabaseAdmin
          .from('qr_codes')
          .insert({
            tenant_id: request.tenant_id,
            session_id: qrSessionId,
            account_id: account.id,
            qr_image_url: qrImageUrl,
            qr_local_path: qrPath,
            status: 'pending',
            expires_at: expiresAt.toISOString(),
            user_agent: request.user_agent,
            metadata: {
              generated_at: new Date().toISOString(),
              qr_generated: qrGenerated
            }
          })
          .select()
          .single();

        if (qrError) {
          logger.error('Failed to create QR record', { error: qrError.message });
        }

        // Store QR session
        this.qrSessions.set(qrSessionId, {
          accountId: account.id,
          tenantId: request.tenant_id,
          api: api,
          expiresAt: expiresAt,
          qrImageUrl: qrImageUrl
        });

        // Setup QR login completion handler
        this.setupQRLoginHandler(qrSessionId, account.id, request.tenant_id, api);

        logger.info('QR login session created successfully', {
          accountId: account.id,
          sessionId: qrSessionId,
          expiresAt: expiresAt.toISOString(),
          qrGenerated: qrGenerated,
          qrFileExists: fs.existsSync(qrPath),
          qrImageUrl: qrImageUrl
        });

        return {
          account_id: account.id,
          qr_code_path: qrPath,
          qr_code_url: qrImageUrl,
          qr_session_id: qrSessionId,
          expires_at: expiresAt.toISOString(),
          status: 'pending'
        };

      } catch (qrError: any) {
        logger.error('QR generation failed', {
          sessionId: qrSessionId,
          error: qrError.message,
          stack: qrError.stack
        });

        // Update account status to error
        await supabaseAdmin
          .from('zalo_accounts')
          .update({
            status: 'error',
            error_data: {
              error_message: qrError.message,
              error_code: 'QR_GENERATION_FAILED',
              retry_count: 0,
              next_retry_at: new Date(Date.now() + 5 * 60 * 1000).toISOString()
            }
          })
          .eq('id', account.id);

        throw new Error(`Unable to login with QRCode: ${qrError.message}`);
      }

    } catch (error: any) {
      logger.error('Failed to generate QR login', {
        tenantId: request.tenant_id,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Setup QR login completion handler
   */
  private setupQRLoginHandler(sessionId: string, accountId: string, tenantId: string, api: any): void {
    // Check for login completion periodically
    const checkInterval = setInterval(async () => {
      try {
        const session = this.qrSessions.get(sessionId);
        if (!session || new Date() > session.expiresAt) {
          clearInterval(checkInterval);
          this.qrSessions.delete(sessionId);
          
          // Update account status to expired
          await this.updateQRStatus(accountId, tenantId, 'expired');
          return;
        }

        // Check if login is successful by trying to get user info
        try {
          const userInfo = await api.fetchAccountInfo();
          if (userInfo && userInfo.userId) {
            clearInterval(checkInterval);

            // Login successful - update account
            await this.completeQRLogin(sessionId, accountId, tenantId, api, userInfo);
          }
        } catch (checkError) {
          // Login not yet complete, continue checking
        }
      } catch (error: any) {
        logger.error('Error in QR login check', { sessionId, error: error.message });
      }
    }, 2000); // Check every 2 seconds

    // Cleanup after expiry
    setTimeout(() => {
      clearInterval(checkInterval);
      this.qrSessions.delete(sessionId);
    }, 15 * 60 * 1000); // 15 minutes max
  }

  /**
   * Complete QR login process
   */
  private async completeQRLogin(sessionId: string, accountId: string, tenantId: string, api: any, userInfo: any): Promise<void> {
    try {
      logger.info('Completing QR login', { sessionId, accountId, userId: userInfo.userId });

      // Get cookies and session data
      const cookies = await api.getCookie();
      const imei = crypto.randomUUID(); // Generate random IMEI for QR login

      // Update account with auth data
      await supabaseAdmin
        .from('zalo_accounts')
        .update({
          zalo_user_id: userInfo.userId,
          zalo_display_name: userInfo.displayName || userInfo.zaloName,
          zalo_phone: userInfo.phoneNumber,
          zalo_avatar_url: userInfo.avatar,
          auth_data: {
            cookies: cookies,
            imei: imei,
            user_agent: userInfo.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            session_token: crypto.randomUUID()
          },
          status: 'active',
          last_login_at: new Date().toISOString(),
          last_activity_at: new Date().toISOString(),
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
          qr_data: {
            ...((await this.getAccount(accountId, tenantId))?.qr_data || {}),
            qr_status: 'confirmed'
          }
        })
        .eq('id', accountId)
        .eq('tenant_id', tenantId);

      // Store Zalo instance
      this.zaloInstances.set(accountId, api);

      // Cleanup QR session
      this.qrSessions.delete(sessionId);

      logger.info('QR login completed successfully', {
        accountId,
        zaloUserId: userInfo.userId,
        displayName: userInfo.displayName
      });

    } catch (error: any) {
      logger.error('Failed to complete QR login', {
        sessionId,
        accountId,
        error: error.message
      });

      await this.updateAccountStatus(accountId, tenantId, 'error', {
        error_message: error.message,
        error_code: 'QR_COMPLETION_FAILED'
      });
    }
  }

  /**
   * Đăng nhập bằng cookie
   */
  async loginWithCookie(request: CookieLoginRequest): Promise<ZaloAccount> {
    try {
      logger.info('Login with cookie', { tenantId: request.tenant_id });

      // Test login with provided credentials
      const zalo = new Zalo({
        selfListen: false,
        checkUpdate: false,
        logging: false
      });

      const api = await zalo.login({
        cookie: request.cookies,
        imei: request.imei,
        userAgent: request.user_agent
      });

      // Get user info to validate login
      const userInfo = await api.fetchAccountInfo();
      if (!userInfo || !userInfo.userId) {
        throw new Error('Invalid credentials - unable to fetch account info');
      }

      const zaloUserId = request.zalo_user_id || userInfo.userId;

      // Check if account already exists
      const { data: existingAccount } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .eq('tenant_id', request.tenant_id)
        .eq('zalo_user_id', zaloUserId)
        .single();

      let account: ZaloAccount;

      if (existingAccount) {
        // Update existing account
        const { data: updatedAccount, error } = await supabaseAdmin
          .from('zalo_accounts')
          .update({
            auth_data: {
              cookies: request.cookies,
              imei: request.imei,
              user_agent: request.user_agent,
              session_token: crypto.randomUUID()
            },
            status: 'active',
            zalo_display_name: request.zalo_display_name || userInfo.displayName || userInfo.zaloName,
            zalo_phone: userInfo.phoneNumber,
            zalo_avatar_url: userInfo.avatar,
            last_login_at: new Date().toISOString(),
            last_activity_at: new Date().toISOString(),
            expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            error_data: {} // Clear any previous errors
          })
          .eq('id', existingAccount.id)
          .select()
          .single();

        if (error) {
          throw new Error(`Failed to update account: ${error.message}`);
        }

        account = updatedAccount;
      } else {
        // Create new account
        const { data: newAccount, error } = await supabaseAdmin
          .from('zalo_accounts')
          .insert({
            tenant_id: request.tenant_id,
            zalo_user_id: zaloUserId,
            zalo_display_name: request.zalo_display_name || userInfo.displayName || userInfo.zaloName,
            zalo_phone: userInfo.phoneNumber,
            zalo_avatar_url: userInfo.avatar,
            auth_data: {
              cookies: request.cookies,
              imei: request.imei,
              user_agent: request.user_agent,
              session_token: crypto.randomUUID()
            },
            status: 'active',
            login_method: 'cookie',
            last_login_at: new Date().toISOString(),
            last_activity_at: new Date().toISOString(),
            expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
          })
          .select()
          .single();

        if (error) {
          throw new Error(`Failed to create account: ${error.message}`);
        }

        account = newAccount;
      }

      // Store Zalo instance
      api.listener.start();
      this.zaloInstances.set(account.id!, api);

      logger.info('Cookie login successful', {
        accountId: account.id,
        zaloUserId: account.zalo_user_id,
        displayName: account.zalo_display_name
      });

      return account;

    } catch (error: any) {
      logger.error('Cookie login failed', {
        tenantId: request.tenant_id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Kiểm tra trạng thái QR code
   */
  async checkQRStatus(accountId: string, tenantId: string): Promise<{ status: string; account?: ZaloAccount }> {
    try {
      const account = await this.getAccount(accountId, tenantId);
      if (!account) {
        return { status: 'not_found' };
      }

      const qrStatus = account.qr_data?.qr_status || 'unknown';

      // Check if QR has expired
      if (account.qr_data?.qr_expires_at) {
        const expiresAt = new Date(account.qr_data.qr_expires_at);
        if (new Date() > expiresAt && qrStatus === 'pending') {
          await this.updateQRStatus(accountId, tenantId, 'expired');
          return { status: 'expired', account };
        }
      }

      return { status: qrStatus, account };

    } catch (error: any) {
      logger.error('Failed to check QR status', {
        accountId,
        tenantId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Lấy trạng thái account
   */
  async getAccountStatus(accountId: string, tenantId: string): Promise<AccountStatusResponse> {
    try {
      const account = await this.getAccount(accountId, tenantId);
      if (!account) {
        throw new Error('Account not found');
      }

      const zaloInstance = this.zaloInstances.get(accountId);
      let isConnected = false;

      // Test connection if instance exists
      if (zaloInstance && account.status === 'active') {
        try {
          await zaloInstance.fetchAccountInfo();
          isConnected = true;

          // Update last activity
          await this.updateAccountActivity(accountId, tenantId);
        } catch (error) {
          isConnected = false;
          logger.warn('Zalo instance connection test failed', {
            accountId,
            error: (error as Error).message
          });
        }
      }

      return {
        account_id: accountId,
        zalo_user_id: account.zalo_user_id,
        status: account.status,
        is_connected: isConnected,
        last_activity_at: account.last_activity_at,
        expires_at: account.expires_at,
        chatwoot_status: account.chatwoot_channel_status,
        error_message: account.error_data?.error_message
      };

    } catch (error: any) {
      logger.error('Failed to get account status', {
        accountId,
        tenantId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Lấy danh sách accounts của tenant
   */
  async getAccountsByTenant(tenantId: string): Promise<ZaloAccount[]> {
    try {
      const { data: accounts, error } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .eq('tenant_id', tenantId)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Failed to get accounts: ${error.message}`);
      }

      return accounts || [];

    } catch (error: any) {
      logger.error('Failed to get accounts by tenant', {
        tenantId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Refresh session cho account
   */
  async refreshSession(accountId: string, tenantId: string): Promise<boolean> {
    try {
      logger.info('Refreshing session', { accountId, tenantId });

      const account = await this.getAccount(accountId, tenantId);
      if (!account) {
        throw new Error('Account not found');
      }

      // Remove old instance
      this.zaloInstances.delete(accountId);

      // Try to reinitialize
      await this.initializeZaloInstance(account);

      // Update last activity
      await this.updateAccountActivity(accountId, tenantId);

      logger.info('Session refreshed successfully', { accountId });
      return true;

    } catch (error: any) {
      logger.error('Failed to refresh session', {
        accountId,
        tenantId,
        error: error.message
      });

      await this.updateAccountStatus(accountId, tenantId, 'error', {
        error_message: error.message,
        error_code: 'REFRESH_FAILED'
      });

      return false;
    }
  }

  /**
   * Xóa account
   */
  async removeAccount(accountId: string, tenantId: string): Promise<boolean> {
    try {
      logger.info('Removing account', { accountId, tenantId });

      // Remove Zalo instance
      const zaloInstance = this.zaloInstances.get(accountId);
      if (zaloInstance) {
        try {
          zaloInstance.listener.stop();
        } catch (error) {
          // Ignore listener stop errors
        }
        this.zaloInstances.delete(accountId);
      }

      // Delete from database
      const { error } = await supabaseAdmin
        .from('zalo_accounts')
        .delete()
        .eq('id', accountId)
        .eq('tenant_id', tenantId);

      if (error) {
        throw new Error(`Failed to delete account: ${error.message}`);
      }

      logger.info('Account removed successfully', { accountId });
      return true;

    } catch (error: any) {
      logger.error('Failed to remove account', {
        accountId,
        tenantId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Lấy Zalo instance cho account
   */
  getZaloInstance(accountId: string): any {
    return this.zaloInstances.get(accountId);
  }

  /**
   * Helper methods
   */
  private async getAccount(accountId: string, tenantId: string): Promise<ZaloAccount | null> {
    const { data, error } = await supabaseAdmin
      .from('zalo_accounts')
      .select('*')
      .eq('id', accountId)
      .eq('tenant_id', tenantId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      throw error;
    }

    return data;
  }

  private async updateAccountStatus(accountId: string, tenantId: string, status: string, errorData?: any): Promise<void> {
    const updateData: any = { status };

    if (errorData) {
      updateData.error_data = errorData;
    }

    await supabaseAdmin
      .from('zalo_accounts')
      .update(updateData)
      .eq('id', accountId)
      .eq('tenant_id', tenantId);
  }

  private async updateQRStatus(accountId: string, tenantId: string, qrStatus: string): Promise<void> {
    const { data: account } = await supabaseAdmin
      .from('zalo_accounts')
      .select('qr_data')
      .eq('id', accountId)
      .eq('tenant_id', tenantId)
      .single();

    if (account) {
      const updatedQRData = {
        ...account.qr_data,
        qr_status: qrStatus,
        updated_at: new Date().toISOString()
      };

      await supabaseAdmin
        .from('zalo_accounts')
        .update({ qr_data: updatedQRData })
        .eq('id', accountId)
        .eq('tenant_id', tenantId);
    }
  }

  private async updateAccountActivity(accountId: string, tenantId: string): Promise<void> {
    await supabaseAdmin
      .from('zalo_accounts')
      .update({
        last_activity_at: new Date().toISOString(),
        status: 'active'
      })
      .eq('id', accountId)
      .eq('tenant_id', tenantId);
  }

  /**
   * Cleanup expired data periodically
   */
  async cleanupExpiredData(): Promise<void> {
    try {
      const { data, error } = await supabaseAdmin.rpc('cleanup_expired_zalo_data');

      if (error) {
        logger.error('Failed to cleanup expired data', { error: error.message });
        return;
      }

      if (data && data.length > 0) {
        const result = data[0];
        logger.info('Cleanup completed', {
          expiredQR: result.expired_qr_count,
          expiredSessions: result.expired_session_count,
          totalCleaned: result.total_cleaned
        });
      }
    } catch (error: any) {
      logger.error('Cleanup process failed', { error: error.message });
    }
  }
}

// Export singleton instance
export const zaloAuthService = new ZaloAuthService();
